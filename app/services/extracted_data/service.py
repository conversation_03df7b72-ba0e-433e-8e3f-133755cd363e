import asyncio
import logging
from typing import Any, Sequence
from uuid import UUID

from constants.extracted_data import ConversationState, DataSourceType, MissingDataStatus, RequiredField
from repositories import ConversationRepository, ExtractedDataRepository, QualsClientsRepository
from schemas import AggregatedData, ConfirmedData, ExtractedData, MissingDataResponse
from services.client_industry import IndustryDataService
from services.project_role import RoleDataService
from services.project_service import ServiceDataService

from .handlers import (
    <PERSON>lient<PERSON><PERSON><PERSON><PERSON><PERSON>,
    DateIntervalsHandler,
    LDMFCountryHandler,
    ObjectiveHandler,
    OutcomesHandler,
)
from .parsers import KXDashDataParser, PromptAndDocumentDataParser


__all__ = ['ExtractedDataService']


logger = logging.getLogger(__name__)


class ExtractedDataService:
    _SOURCE_TYPE_TO_PARSER_MAP = {
        DataSourceType.KX_DASH: KXDashDataParser,
        DataSourceType.DOCUMENTS: PromptAndDocumentDataParser,
        DataSourceType.PROMPT: PromptAndDocumentDataParser,
    }

    _SOURCE_DATA_PROCESSING_PRIORITY = [DataSourceType.KX_DASH, DataSourceType.DOCUMENTS, DataSourceType.PROMPT]

    _FIELD_HANDLERS = {
        RequiredField.LDMF_COUNTRY: LDMFCountryHandler(),
        RequiredField.ENGAGEMENT_DATES: DateIntervalsHandler(),
        RequiredField.OBJECTIVE_SCOPE: ObjectiveHandler(),
        RequiredField.OUTCOMES: OutcomesHandler(),
    }

    # Define the order in which fields should be collected
    _FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_INFO,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]

    # Map fields to conversation states
    _FIELD_TO_STATE_MAP = {
        RequiredField.CLIENT_INFO: ConversationState.COLLECTING_CLIENT_NAME,
        RequiredField.LDMF_COUNTRY: ConversationState.COLLECTING_COUNTRY,
        RequiredField.ENGAGEMENT_DATES: ConversationState.COLLECTING_DATES,
        RequiredField.OBJECTIVE_SCOPE: ConversationState.COLLECTING_OBJECTIVE,
        RequiredField.OUTCOMES: ConversationState.COLLECTING_OUTCOMES,
    }

    def __init__(
        self,
        industry_data_service: IndustryDataService,
        role_data_service: RoleDataService,
        service_data_service: ServiceDataService,
        extracted_data_repository: ExtractedDataRepository,
        conversation_repository: ConversationRepository,
        quals_clients_repository: QualsClientsRepository,
    ):
        self.extracted_data_repository = extracted_data_repository
        self.industry_data_service = industry_data_service
        self.role_data_service = role_data_service
        self.service_data_service = service_data_service
        self.conversation_repository = conversation_repository
        self.quals_clients_repository = quals_clients_repository
        self._FIELD_HANDLERS[RequiredField.CLIENT_INFO] = ClientNameHandler(quals_clients_repository)

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data from.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def _process_activity_data(self, activity_data: dict[str, Any]) -> dict[str, Any]:
        """
        Process the activity data.
        """
        industries, services, roles = await asyncio.gather(
            self.industry_data_service.list(),
            self.service_data_service.list(),
            self.role_data_service.list(),
            return_exceptions=True,
        )

        industry_name = activity_data.get('global_industry')
        if industry_name:
            industry = industries.get(industry_name)
            if industry:
                activity_data['industries'] = [industry['id']]

        service_name = activity_data.get('global_service')
        if service_name:
            service = services.get(service_name)
            if service:
                activity_data['services'] = [service['id']]

        lep_emails = activity_data.get('engagement_lep_emails')
        lcsp_emails = activity_data.get('engagement_lcsp_emails')
        manager_emails = activity_data.get('engagement_manager_emails')

        processed_roles = []
        if lep_emails:
            lep_role_id = roles.get('Lead Engagement Partner', {}).get('id')
            processed_roles.extend({'email': email, 'roles': [lep_role_id]} for email in lep_emails)
        if lcsp_emails:
            lcsp_role_id = roles.get('LCSP', {}).get('id')
            processed_roles.extend({'email': email, 'roles': [lcsp_role_id]} for email in lcsp_emails)
        if manager_emails:
            manager_role_id = roles.get('Engagement Manager', {}).get('id')
            processed_roles.extend({'email': email, 'roles': [manager_role_id]} for email in manager_emails)

        activity_data['roles'] = processed_roles

        return activity_data

    async def update(
        self, conversation_id: UUID, raw_data: dict[str, Any], source_type: DataSourceType
    ) -> ExtractedData:
        """
        Update extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to update extracted data for.
            raw_data: The data to update the extracted data with.
            source_type: The type of data source being updated.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
            ServiceExceptionError: If the extracted data record is not found.
        """
        logger.debug('Started updating "%s" extracted data for conversation ID "%s"', source_type, conversation_id)
        try:
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=source_type,
            ) or ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
            processed_activity_data = await self._process_activity_data(raw_data)
            parser = self._SOURCE_TYPE_TO_PARSER_MAP[source_type]()
            await self.extracted_data_repository.update(parser(extracted_data, processed_activity_data))
            return extracted_data

        except Exception as e:
            logger.error(
                'Failed to update "%s" extracted data for conversation ID "%s": %s', source_type, conversation_id, e
            )
            raise

    async def aggregate_data(self, conversation_id: UUID) -> AggregatedData:
        """
        Aggregate data from all sources for a conversation, applying different strategies:
        - client_name, ldmf_country, title: Merge unique values from all sources
        - date_intervals: Collect date ranges as (start_date, end_date) pairs from all sources
        - objective_and_scope, outcomes: Priority-based override (highest priority wins)

        Args:
            conversation_id: The ID of the conversation to aggregate data for.

        Returns:
            AggregatedData: The aggregated data from all sources.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        # Initialize collections for merging
        all_client_names = set()
        all_ldmf_countries = set()
        all_date_intervals = set()

        # Initialize priority-based fields
        objective_and_scope = None
        outcomes = None

        # Process data sources in priority order (lowest to highest)
        for source_type in self._SOURCE_DATA_PROCESSING_PRIORITY:
            try:
                extracted_data = await self.extracted_data_repository.get(
                    conversation_id=conversation_id,
                    data_source_type=source_type,
                )
                if not extracted_data:
                    logger.debug('No %s data found for conversation %s', source_type, conversation_id)
                    continue
                # Merge unique values for client_name, ldmf_country
                if extracted_data.client_name:
                    all_client_names.update(extracted_data.client_name)

                if extracted_data.ldmf_country:
                    all_ldmf_countries.update(extracted_data.ldmf_country)

                # Collect date intervals
                if extracted_data.start_date or extracted_data.end_date:
                    start_date_str = extracted_data.start_date.isoformat() if extracted_data.start_date else None
                    end_date_str = extracted_data.end_date.isoformat() if extracted_data.end_date else None
                    if start_date_str or end_date_str:
                        all_date_intervals.add((start_date_str or '', end_date_str or ''))

                # Priority-based override for other fields
                objective_and_scope = extracted_data.objective_and_scope or objective_and_scope
                outcomes = extracted_data.outcomes or outcomes

            except Exception as e:
                logger.warning('Error processing %s data for conversation %s: %s', source_type, conversation_id, e)
                continue
        return AggregatedData(
            client_name=sorted(list(all_client_names)),
            ldmf_country=sorted(list(all_ldmf_countries)),
            date_intervals=list(all_date_intervals),
            objective_and_scope=objective_and_scope,
            outcomes=outcomes,
        )

    async def get_missing_required_data_prompts(
        self, conversation_id: UUID, confirmed_data: ConfirmedData | None = None
    ) -> MissingDataResponse:
        """
        Method for progressive data collection with user confirmation tracking.

        Args:
            conversation_id: The ID of the conversation to check.
            confirmed_data: User-confirmed data from previous interactions.

        Returns:
            MissingDataResponse: Structured response with next steps for data collection.
        """
        logger.debug('Enhanced missing data check for conversation ID: %s', conversation_id)

        if confirmed_data is None:
            confirmed_data = ConfirmedData()

        try:
            aggregated_data = await self.aggregate_data(conversation_id)

            # Find the first field that needs confirmation
            for field_type in self._FIELD_COLLECTION_ORDER:
                handler = self._FIELD_HANDLERS[field_type]
                response = await handler.check_and_get_response(aggregated_data, confirmed_data)

                if response.needs_confirmation:
                    # Found a field that needs confirmation
                    missing_fields = await self._get_all_missing_fields(aggregated_data, confirmed_data)

                    return MissingDataResponse(
                        status=MissingDataStatus.MISSING_DATA,
                        message=response.system_message,
                        next_expected_field=response.next_expected_field,
                        missing_fields=missing_fields,
                        conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                        options=response.options,
                    )

            # All fields are confirmed
            return MissingDataResponse(
                status=MissingDataStatus.DATA_COMPLETE,
                message=None,
                next_expected_field=None,
                missing_fields=[],
                conversation_state=ConversationState.DATA_COMPLETE,
            )

        except Exception as e:
            logger.error("Failed to check enhanced missing data for conversation ID '%s': %s", conversation_id, e)
            return MissingDataResponse(
                status=MissingDataStatus.ERROR,
                message='An error occurred while checking data completeness.',
                next_expected_field=None,
                missing_fields=[],
                conversation_state=ConversationState.INITIAL,
            )

    async def _get_all_missing_fields(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> list[str]:
        """Get list of all fields that still need confirmation."""
        missing_fields = []

        for field_type in self._FIELD_COLLECTION_ORDER:
            handler = self._FIELD_HANDLERS[field_type]
            response = await handler.check_and_get_response(aggregated_data, confirmed_data)

            if response.needs_confirmation:
                missing_fields.append(response.next_expected_field or field_type.value)

        return missing_fields

    async def list(
        self, data_source_type: DataSourceType, activity_ids: Sequence[str] | None = None
    ) -> Sequence[ExtractedData]:
        """
        Get extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to get extracted data for.
            data_source_type: The type of data source to get extracted data for.

        Raises:
            Exception: If there's an error listing the extracted data.
        """
        try:
            return await self.extracted_data_repository.list(data_source_type, activity_ids=activity_ids)
        except Exception as e:
            logger.error("Failed to get extracted data for data source type '%s': %s", data_source_type, e)
            raise

    async def delete(self, conversation_id: UUID, data_source_type: DataSourceType) -> None:
        """
        Delete extracted data of a specified source type for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data for.
            data_source_type: The type of data source to delete extracted data for.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete(conversation_id, data_source_type)

        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def update_confirmed_data(
        self, conversation_id: UUID, field_name: str, field_value: str, state: ConversationState
    ) -> None:
        """
        Update confirmed data for a specific field and update conversation state.

        Args:
            conversation_id: The ID of the conversation to update
            field_name: The name of the field to update (e.g., 'client_name')
            field_value: The confirmed value for the field
            state: The new conversation state

        Raises:
            EntityNotFoundError: If the conversation does not exist
        """
        logger.debug('Updating confirmed data for conversation ID: %s, field: %s', conversation_id, field_name)

        try:
            # Get current confirmed data
            current_confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

            # Update the specific field
            updated_data = current_confirmed_data.model_copy()
            setattr(updated_data, field_name, field_value)

            # Update both confirmed data and state
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id, confirmed_data=updated_data, state=state
            )

            logger.debug('Successfully updated confirmed data for conversation ID: %s', conversation_id)

        except Exception as e:
            logger.error('Failed to update confirmed data for conversation ID: %s: %s', conversation_id, e)
            raise
