from typing import Any, Sequence

from constants.message import DASH_TASK_SELECTED_TEMPLATE, WELCOME_MESSAGE_WITH_ONE_DASH_TASK


__all__ = ['kx_dash_message_formatter']


class KXDashMessageFormatter:
    _DEFAULT_CLIENT_REPR = 'this_client'
    _DEFAULT_ACTIVITY_REPR = 'activity'

    def __call__(self, activity: dict[str, Any], is_welcome_message: bool = False) -> str:
        """
        Format the dash task selected message template with activity data.

        Args:
            activity: The dash task activity data

        Returns:
            Formatted message string
        """
        details: list[str] = []

        # Basic info
        client_name = activity.get('client_name', 'this client')
        activity_name = activity.get('activity_name', 'activity')

        if client_name != 'this client':
            details.extend(self._get_detail('Client Name', client_name))

        if activity_name != 'activity':
            details.extend(self._get_detail('Engagement Title', activity_name))

        # Location info with efficient handling
        member_firm = activity.get('member_firm')
        country = activity.get('country')

        if member_firm and country:
            details.extend(self._get_detail('Lead Deloitte Member Firm/Country', f'{member_firm}/{country}'))
        elif member_firm:
            details.extend(self._get_detail('Lead Deloitte Member Firm', member_firm))
        elif country:
            details.extend(self._get_detail('Lead Deloitte Country', country))

        # Dates
        details.extend(self._get_detail('Start Date', activity.get('engagement_start_date')))
        details.extend(self._get_detail('End Date', activity.get('engagement_end_date')))

        # Industry info with hierarchical display
        industry_fields = {
            'Global Industry': activity.get('global_industry'),
            'Global Industry Sector': activity.get('global_industry_sector'),
        }
        details.extend(self._get_section("Client's Industry", industry_fields))

        # Services with hierarchical display
        service_fields = {
            'Global Business': activity.get('global_business'),
            'Service Area': activity.get('global_business_service_area'),
            'Service Line': activity.get('global_business_service_line'),
        }
        details.extend(self._get_section('Client Services', service_fields))

        # Team roles section
        team_fields = {
            'Global LCSP': activity.get('global_lcsp_emails'),
            'Engagement LEP': activity.get('engagement_lep_emails'),
            'Engagement Manager': activity.get('engagement_manager_emails'),
        }
        details.extend(self._get_section('Team & Roles', team_fields, join_lists=True))

        # Return the formatted template
        template = WELCOME_MESSAGE_WITH_ONE_DASH_TASK if is_welcome_message else DASH_TASK_SELECTED_TEMPLATE
        return template.format(activity_name=activity_name, client_name=client_name, details='\n'.join(details))

    @classmethod
    def _get_section(cls, section_title: str, fields_dict: dict[str, Any], join_lists: bool = False) -> Sequence[str]:
        """
        Get a section representation
        """
        # Check if any field in this section has a value
        if not any(fields_dict.values()):
            return ()

        result = []
        result.extend(cls._get_detail(section_title))
        for label, value in fields_dict.items():
            if value:
                if join_lists and isinstance(value, list):
                    formatted_value = ', '.join(value)
                    result.extend(cls._get_detail(label, formatted_value, prefix='  - '))
                else:
                    result.extend(cls._get_detail(label, value, prefix='  - '))
        return result

    @staticmethod
    def _get_detail(label: str, value: Any = None, prefix: str = '• ') -> Sequence[str]:
        """
        Get detail representation
        """
        if value is None:  # For headers (when value is None), just add the label with a colon
            return (f'{prefix}{label}:',)

        if value:  # For regular details with values
            return (f'{prefix}{label}: {value}',)

        return ()


kx_dash_message_formatter = KXDashMessageFormatter()
