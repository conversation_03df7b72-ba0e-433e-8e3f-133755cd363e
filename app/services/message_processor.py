from dataclasses import dataclass
import logging
from typing import Any
from uuid import UUID

from fastapi import UploadFile

from constants.extracted_data import ConversationState, MissingDataStatus
from constants.message import (
    BRIEF_DESCRIPTION_REPLY,
    CLIENT_NAME_CONFIRMED,
    EXAMPLE_REPLY,
    UNDEFINED_REPLY,
    ConversationMessageIntention,
)
from repositories import ConversationRepository
from schemas import ConversationMessageIntentClassifierServiceResponse, ConversationMessageProcessingResult

from .extracted_data import ExtractedDataService
from .intent_classifier import IntentClassifierService


logger = logging.getLogger(__name__)
@dataclass(frozen=True)
class ConversationMessageProcessor:
    """Processor for conversation message intention."""

    conversation_id: UUID
    user_message: str
    files: list[UploadFile] | None
    intent_classifier_service: IntentClassifierService
    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository

    async def _get_intent(self) -> ConversationMessageIntention:
        """Intent getter."""
        intent_classification_response: ConversationMessageIntentClassifierServiceResponse = (
            await self.intent_classifier_service.classify_intent(
                user_message=self.user_message,
                response_cls=ConversationMessageIntentClassifierServiceResponse,
            )
        )
        intention = intent_classification_response.intention
        logger.info(f'Classified intention: {intention}')
        return intention

    async def run(self) -> ConversationMessageProcessingResult:
        """Process the intention."""

        if not self.user_message:
            raise ValueError('You cannot classify intent with empty user message.')

        intention = await self._get_intent()

        if intention == ConversationMessageIntention.UNDEFINED:
            data = self._process_undefined()

        elif intention == ConversationMessageIntention.GENERATE_QUAL:
            data = self._generate_qual()
        elif intention == ConversationMessageIntention.EXTRACTION:
            data = self._extract_data()
        elif intention == ConversationMessageIntention.EXAMPLE:
            data = self._example_help()
        elif intention == ConversationMessageIntention.DASH_DISCARD:
            data = self._dash_discard()
        elif intention == ConversationMessageIntention.UNCERTAINITY:
            data = await self._uncertainty()
        elif intention == ConversationMessageIntention.NEED_CONTEXT:
            data = await self._uncertainty()
        else:
            raise NotImplementedError(f'Intent {intention} not implemented')

        system_reply = data.pop('system_reply')
        return ConversationMessageProcessingResult(
            intention=intention,
            system_reply=system_reply,
            data=data,
        )

    def _process_undefined(self) -> dict[str, Any]:
        return {'system_reply': UNDEFINED_REPLY}

    def _generate_qual(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to generate a qual
        # ...
        system_reply = f'Finished processing `_generate_qual` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _extract_data(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to extract data
        # ...
        system_reply = f'Finished processing `_extract_data` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _dash_show_more(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to show more dash tasks
        # ...
        system_reply = f'Finished processing `_dash_show_more` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _dash_discard(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to discard dash tasks
        # ...
        system_reply = f'Finished processing `_dash_discard` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _example_help(self) -> dict[str, Any]:
        return {'system_reply': EXAMPLE_REPLY}

    async def _uncertainty(self) -> dict[str, Any]:
        """
        Handle uncertainty intention with progressive data collection.

        This method triggers the missing data collection flow, checking what
        information is still needed and guiding the user through confirmation.
        """
        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise ValueError(f'Conversation {self.conversation_id} not found')

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)

            # Check if user is providing manual input for client name
            if (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and self.user_message.strip()
            ):
                # Extract client name from user message and save it
                client_name = self.user_message.strip()
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name='client_name',
                    field_value=client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )

                confirmation_message = CLIENT_NAME_CONFIRMED.format(client_name=client_name)
                return {
                    'system_reply': confirmation_message,
                    'missing_data_status': MissingDataStatus.MISSING_DATA,
                }

            # Check what data is missing using the enhanced service
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id, confirmed_data=confirmed_data
            )

            # Update conversation state based on the response
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )

                return {
                    'system_reply': missing_data_response.message,
                    'missing_data_status': missing_data_response.status,
                    'next_expected_field': missing_data_response.next_expected_field,
                    'missing_fields': missing_data_response.missing_fields,
                    'options': missing_data_response.options,
                    'conversation_state': missing_data_response.conversation_state,
                }

            elif missing_data_response.status == MissingDataStatus.DATA_COMPLETE:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )

                return {
                    # 'system_reply': 'Great! I have all the information needed to create your qual. Would you like me to generate it now?',
                    'system_reply': BRIEF_DESCRIPTION_REPLY,  # Fallback to original behavior
                    'missing_data_status': missing_data_response.status,
                }

            else:  # error status
                logger.error(
                    'Error in missing data collection for conversation %s: %s',
                    self.conversation_id,
                    missing_data_response.message,
                )
                return {
                    'system_reply': BRIEF_DESCRIPTION_REPLY,  # Fallback to original behavior
                    'missing_data_status': MissingDataStatus.ERROR,
                }

        except Exception as e:
            logger.error('Exception in _uncertainty method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            return {'system_reply': BRIEF_DESCRIPTION_REPLY}