import logging
from typing import Any

from httpx import AsyncClient

from core.backoff import expo_backoff


__all__ = ['BaseHttpRepository']

logger = logging.getLogger(__name__)


class BaseHttpRepository:
    """Repository for HTTP API operations."""

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the HTTP Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
            base_path: The base path for the API endpoints
        """
        self._http_client = http_client

    @expo_backoff
    async def _execute_request(self, method: str, url: str, **kwargs) -> Any:
        """
        Execute an HTTP request with retry capability.

        Args:
            method: HTTP method (get, post, etc.)
            url: The URL to request
            **kwargs: Additional arguments to pass to the request

        Returns:
            Any: The JSON response from the API

        Raises:
            HTTPStatusError: If the request fails with a non-retryable status code
            TransportError: If there's a network error that can't be retried
        """
        response = await self._http_client.request(method.upper(), url, **kwargs)
        response.raise_for_status()
        return response.json()
