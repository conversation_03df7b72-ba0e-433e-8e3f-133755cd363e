import logging
from typing import Any

from httpx import AsyncClient

from config import settings
from core.repositories import BaseHttpRepository


__all__ = ['RoleRepository']

logger = logging.getLogger(__name__)


class RoleRepository(BaseHttpRepository):
    """Repository for Role API operations."""

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the Role Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
        """
        super().__init__(http_client)
        self._base_path = settings.roles_api.base_url

    async def list(self) -> list[dict[str, Any]]:
        """
        List all roles.

        Returns:
            list[dict[str, Any]]: A list of roles

        Raises:
            Exception: If an error occurs while listing roles
        """
        url = f'{self._base_path}/project-roles'
        try:
            return await self._execute_request('GET', url)
        except Exception as e:
            logger.error('Error listing roles: %s', e)
            raise e
