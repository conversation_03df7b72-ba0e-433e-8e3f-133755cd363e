from datetime import date, <PERSON><PERSON><PERSON>
from typing import Any
from uuid import uuid4

from httpx import AsyncClient

from config import settings
from core.repositories import BaseHttpRepository


__all__ = ['KXDashRepository']


class KXDashRepository(BaseHttpRepository):
    """Repository for KX Dash API operations."""

    # Class attributes to control mock responses
    MOCK_LIST_COUNT = 2  # Number of mock tasks to return for list_activities (0, 1, 2)
    MOCK_GET_ENABLED = True  # Whether get should return a task (True) or None (False)

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the KX Dash Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
        """
        super().__init__(http_client)
        self._base_path = settings.kx_dash_api.base_url

    def _generate_mock_task(self, activity_id: int) -> dict[str, Any]:
        """
        Generate a mock task for testing.

        Args:
            activity_id: Optional specific activity ID to use, otherwise random

        Returns:
            dict[str, Any]: A mock task response
        """
        task_id = activity_id or 100000 + (hash(str(uuid4())) % 10000)
        today = date.today()

        return {
            'activityId': task_id,
            'activityName': f'Mock Quality Review {task_id}',
            'clientName': 'Mock Client Corporation',
            'memberFirm': 'US',
            'country': 'United States',
            'globalBusiness': 'Audit & Assurance',
            'globalBusinessServiceArea': 'Audit',
            'globalBusinessServiceLine': 'External Audit',
            'globalIndustry': 'Financial Services',
            'globalIndustrySector': 'Banking',
            'engagementCode': f'ENG-{task_id}',
            'globalLCSPEmails': [f'lcsp{task_id}@example.com', '<EMAIL>'],
            'engagementLepEmails': [f'lep{task_id}@example.com', '<EMAIL>'],
            'engagementManagerEmails': [f'manager{task_id}@example.com', '<EMAIL>'],
            'activityOwnerEmails': [f'owner{task_id}@example.com', '<EMAIL>'],
            'engagementStartDate': (today - timedelta(days=30)).isoformat(),
            'engagementEndDate': (today + timedelta(days=60)).isoformat(),
        }

    async def list(self) -> list[dict[str, Any]]:
        """
        Mock implementation of the Activity list endpoint.
        Returns 0, 1, or 2 mock tasks based on the mock_list_count class attribute.

        Returns:
            list[dict[str, Any]]: Mock response with a list of activities
        """
        # In real implementation:
        # url = f"{self._base_path}/searchActivitiesForOwner"
        # try:
        #     params = {"query": query} if query else {}
        #     return await self._execute_request("GET", url, params=params)
        # except Exception as e:
        #     logger.error(f"Error fetching activities: {e}")
        #     return {"success": False, "message": str(e), "data": {"activities": [], "total": 0}}

        # Mock implementation:
        tasks = []
        for i in range(self.MOCK_LIST_COUNT):
            tasks.append(self._generate_mock_task(activity_id=100001 + i))

        return tasks

    async def get(self, activity_id: int) -> dict[str, Any]:
        """
        Mock implementation of the get activity endpoint.
        Returns a mock task if mock_get_enabled is True, None otherwise.

        Args:
            activity_id: The ID of the activity to retrieve

        Returns:
            dict[str, Any] | None: Mock activity data or None
        """
        # In real implementation:
        # url = f"{self._base_path}/getActivityDataById"
        # try:
        #     return await self._execute_request("GET", url, params={"activityId": activity_id})
        # except Exception as e:
        #     logger.error(f"Error fetching activity {activity_id}: {e}")
        #     return {"success": False, "message": str(e), "data": None}

        # Mock implementation:
        return self._generate_mock_task(activity_id=activity_id)
