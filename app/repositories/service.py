import logging
from typing import Any

from httpx import AsyncClient

from config import settings
from core.repositories import BaseHttpRepository


__all__ = ['ServiceRepository']

logger = logging.getLogger(__name__)


class ServiceRepository(BaseHttpRepository):
    """Repository for Service API operations."""

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the Service Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
        """
        super().__init__(http_client)
        self._base_path = settings.services_api.base_url

    async def list(self) -> list[dict[str, Any]]:
        """
        List all services.

        Returns:
            list[dict[str, Any]]: A list of services

        Raises:
            Exception: If an error occurs while listing services
        """
        url = f'{self._base_path}/services-all'
        try:
            return await self._execute_request('GET', url)
        except Exception as e:
            logger.error('Error listing services: %s', e)
            raise e
