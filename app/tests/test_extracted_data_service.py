from datetime import UTC, date, datetime
import json
from uuid import UUID

from constants.extracted_data import DataSourceType
from schemas.extracted_data import ExtractedData


class TestExtractedDataService:
    async def test_aggregate_data_prioritizes_sources_correctly(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        # Call the method
        result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # Verify the result has merged unique values for client_name, ldmf_country, title
        expected_client_names = sorted(['KX Client', 'Document Client', 'Prompt Client'])
        expected_ldmf_countries = sorted(['KX Country', 'Document Country', 'Prompt Country'])

        assert result.client_name == expected_client_names
        assert result.ldmf_country == expected_ldmf_countries

        # Verify date intervals are collected from all sources
        assert len(result.date_intervals) == 3
        assert ('2023-01-01', '2023-12-31') in result.date_intervals  # KX Dash
        assert ('2023-02-01', '2023-11-30') in result.date_intervals  # Documents
        assert ('2023-03-01', '2023-10-31') in result.date_intervals  # Prompt

        # Verify priority-based fields use highest priority (prompt)
        assert result.objective_and_scope == prompt_extracted_data.objective_and_scope
        assert result.outcomes == prompt_extracted_data.outcomes

    async def test_aggregate_data_fills_missing_prompt_fields_from_other_sources(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Make prompt_extracted_data incomplete (missing client_name and ldmf_country)
        prompt_extracted_data.client_name = []
        prompt_extracted_data.ldmf_country = []

        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # Client names and countries should be merged from all sources (prompt has empty lists)
        expected_client_names = sorted(['KX Client', 'Document Client'])
        expected_ldmf_countries = sorted(['KX Country', 'Document Country'])
        assert result.client_name == expected_client_names
        assert result.ldmf_country == expected_ldmf_countries

    async def test_aggregate_data_uses_other_sources_when_prompt_empty(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Make prompt_extracted_data empty
        for field in [
            'activity_name',
            'client_name',
            'ldmf_country',
            'title',
            'start_date',
            'end_date',
            'industries',
            'services',
            'roles',
            'objective_and_scope',
            'outcomes',
        ]:
            setattr(
                prompt_extracted_data,
                field,
                None if not isinstance(getattr(prompt_extracted_data, field), list) else [],
            )

        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # All fields should be filled from the next available source (documents or kx_dash)
        # Client names and countries should be merged from all sources (prompt is empty)
        expected_client_names = sorted(['KX Client', 'Document Client'])
        expected_ldmf_countries = sorted(['KX Country', 'Document Country'])
        assert result.client_name == expected_client_names
        assert result.ldmf_country == expected_ldmf_countries

    def test_extracted_data_schema(self):
        source_type = DataSourceType.PROMPT
        conversation_id = UUID('00000000-0000-0000-0000-000000000001')
        extracted = ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
        assert extracted.conversation_id == conversation_id
        assert extracted.data_source_type == source_type
        assert extracted.created_at is not None
        model_dump = extracted.model_dump()
        assert model_dump['data_source_type'] == source_type
        assert model_dump['conversation_id'] == conversation_id

        source_type = DataSourceType.KX_DASH
        conversation_id = UUID('00000000-0000-0000-0000-000000000001')
        extracted = ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
        extracted.services = json.dumps({'level_1': 'Test Service'})
        extracted.industries = json.dumps({'level_1': 'Test Industry'})
        extracted.roles = json.dumps({'team_members': ['Test Team Member']})

        model_dump = extracted.model_dump()
        assert model_dump['services'] == json.dumps({'level_1': 'Test Service'})
        assert model_dump['industries'] == json.dumps({'level_1': 'Test Industry'})
        assert model_dump['roles'] == json.dumps({'team_members': ['Test Team Member']})
        assert model_dump['conversation_id'] == conversation_id
        assert model_dump['data_source_type'] == source_type

        data = ExtractedData(
            ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
            DataSourceType=DataSourceType.PROMPT,
            CreatedAt=datetime.now(UTC),
            ClientName=json.dumps(['Client1', 'Client2']),  # type: ignore
            LDMFCountry=json.dumps(['Country1']),  # type: ignore
            Services=json.dumps({'service': 'test'}),
            Roles=json.dumps({'role': 'test'}),
            ActivityName='Test Activity',
            Title='Test Title',
            StartDate=date(2023, 1, 1),
            EndDate=date(2023, 12, 31),
            Industries=json.dumps({'level_1': 'Test Industry'}),
            ObjectiveAndScope='Test Objective and Scope',
            Outcomes='Test Outcomes',
        )

        result = data.model_dump_for_db()

        # Should not contain these fields
        assert 'ConversationPublicId' not in result
        assert 'CreatedAt' not in result

        # Should be JSON strings
        assert result['ClientName'] == json.dumps(['Client1', 'Client2'])
        assert result['LDMFCountry'] == json.dumps(['Country1'])
        assert result['Services'] == json.dumps({'service': 'test'})
        assert result['Roles'] == json.dumps({'role': 'test'})
        assert result['Industries'] == json.dumps({'level_1': 'Test Industry'})
        assert result['ObjectiveAndScope'] == 'Test Objective and Scope'
        assert result['Outcomes'] == 'Test Outcomes'
        assert result['ActivityName'] == 'Test Activity'
        assert result['Title'] == 'Test Title'
        assert result['StartDate'] == date(2023, 1, 1)
        assert result['EndDate'] == date(2023, 12, 31)
        assert result['DataSourceType'] == DataSourceType.PROMPT
