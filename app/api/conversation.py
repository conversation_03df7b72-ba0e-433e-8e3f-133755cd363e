import logging
from typing import Sequence
from uuid import UUID

from fastapi import APIRouter, HTTPException, Request, status

from constants.operation_ids import operation_ids
from dependencies import ConversationMessageServiceDep, ConversationServiceDep, OwnerOnlyPermissionDep
from exceptions import EntityNotFoundError
from schemas import (
    BaseMessageSerializer,
    ConversationCreationRequest,
    ConversationResponse,
    ConversationWithWelcomeMessageResponse,
    MessageSerializer,
)


__all__ = ['router']

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/conversations')


@router.post(
    '',
    operation_id=operation_ids.conversation.CREATE,
    status_code=status.HTTP_201_CREATED,
)
async def create_conversation_with_welcome_message(
    conversation_data: ConversationCreationRequest,
    conversation_service: ConversationServiceDep,
    request: Request,
) -> ConversationWithWelcomeMessageResponse:
    """
    Create a new conversation with a welcome system message.

    This endpoint creates a new conversation and automatically adds a system welcome message.
    """
    # TODO: When Dash task support is added, implement passing of Dash task data to conversation

    try:
        return await conversation_service.create_with_welcome_message(
            conversation_data, user_id=request.state.user.id, user_name=request.state.user.full_name
        )

    except Exception as e:
        logger.error('Error creating conversation with welcome message: %s', e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to create conversation with welcome message: {str(e)}',
        )


@router.get(
    '/{conversation_id}',
    operation_id=operation_ids.conversation.GET,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get_conversation_by_id(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> ConversationResponse:
    """
    Get a conversation by its ID.
    """
    try:
        conversation = await conversation_service.get(conversation_id)
        return conversation
    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception:
        logger.exception('Error retrieving conversation')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Failed to retrieve conversation',
        )


@router.get(
    '/{conversation_id}/messages',
    operation_id=operation_ids.message.LIST,
    status_code=status.HTTP_200_OK,
    response_model=Sequence[MessageSerializer],
    dependencies=(OwnerOnlyPermissionDep,),
)
async def list_messages(
    conversation_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> Sequence[BaseMessageSerializer]:
    """
    Get all messages for a specific conversation.
    """
    try:
        return await message_service.list(conversation_id)

    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )

    except Exception as e:
        logger.exception('Error retrieving messages for conversation')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to retrieve conversation messages: {str(e)}',
        )


@router.delete(
    '/{conversation_id}',
    operation_id=operation_ids.conversation.DELETE,
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def delete_conversation(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> None:
    """
    Delete a conversation by its ID.

    This operation also deletes all messages associated with the conversation.
    """
    try:
        await conversation_service.delete(conversation_id)
    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception:
        logger.exception('Error deleting conversation')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Failed to delete conversation',
        )


@router.get(
    '/{conversation_id}/messages/last',
    operation_id=operation_ids.message.GET_LAST,
    status_code=status.HTTP_200_OK,
    response_model=MessageSerializer,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get_last_message(
    conversation_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> BaseMessageSerializer:
    """
    Get the last message for a specific conversation.
    """
    try:
        return await message_service.get_last(conversation_id)

    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )

    except Exception as e:
        err_msg = 'Failed to retrieve the last message for the conversation'
        logger.exception(err_msg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'{err_msg}: {str(e)}',
        )
